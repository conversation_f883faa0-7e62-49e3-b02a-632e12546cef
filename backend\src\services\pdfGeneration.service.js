import { chromium } from 'playwright';
import { generateReportTemplate, generateComponentsHTML } from '../utils/reportTemplateGenerator.js';
import path from 'path';
import fs from 'fs';

/**
 * PDF Generation Service using Playwright
 */
export class PDFGenerationService {
  constructor() {
    this.browser = null;
  }

  /**
   * Initialize browser instan ce
   */
  async initBrowser() {
    if (!this.browser) {
      this.browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });
    }
    return this.browser;
  }

  /**
   * Close browser instance
   */
  async closeBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }
  }

  /**
   * Navigate to URL and wait for content to load
   * @param {Object} page - Playwright page instance
   * @param {string} url - URL to navigate to
   * @param {string} selector - CSS selector to wait for
   */
  async navigateToURL(page, url, selector) {
    // Navigate to URL with increased timeout
    console.log(`Navigating to URL: ${url}`);
    await page.goto(url, {
      waitUntil: 'domcontentloaded',
      timeout: 90000 // Increased timeout to 90 seconds
    });

    // Wait for specific selector to ensure content is loaded with multiple attempts
    if (selector) {
      console.log(`Waiting for selector: ${selector}`);
      let attempts = 0;
      const maxAttempts = 3;

      while (attempts < maxAttempts) {
        try {
          await page.waitForSelector(selector, { timeout: 45000 }); // Increased timeout to 45 seconds
          console.log(`Selector ${selector} found on attempt ${attempts + 1}`);
          break;
        } catch (error) {
          attempts++;
          console.log(`Attempt ${attempts} failed to find selector ${selector}. ${maxAttempts - attempts} attempts remaining.`);

          if (attempts >= maxAttempts) {
            // Try alternative selectors as fallback
            const fallbackSelectors = ['.report-container', '[class*="report"]', 'main', 'body'];
            let fallbackFound = false;

            for (const fallbackSelector of fallbackSelectors) {
              try {
                await page.waitForSelector(fallbackSelector, { timeout: 10000 });
                console.log(`Fallback selector ${fallbackSelector} found`);
                fallbackFound = true;
                break;
              } catch (fallbackError) {
                continue;
              }
            }

            if (!fallbackFound) {
              console.warn(`Could not find selector ${selector} or any fallback selectors. Proceeding anyway.`);
            }
          } else {
            // Wait a bit before retrying
            await page.waitForTimeout(2000);
          }
        }
      }
    }

    // Wait for any charts or dynamic content to render
    console.log('Waiting for content to render...');
    await page.waitForTimeout(3000); // Reduced from 5000ms to 3000ms
  }

  /**
   * Generate PDF from report components
   * @param {Object} options - PDF generation options
   * @param {Object} options.templateSettings - Template styling settings
   * @param {Object} options.components - Report components HTML content
   * @param {string} options.companyName - Company name for the report
   * @param {string} options.reportTitle - Title of the report
   * @returns {Buffer} - PDF buffer
   */
  async generateReportPDF(options) {
    const {
      templateSettings = {},
      components = {},
      companyName = 'Company Report',
      reportTitle = 'Financial Report'
    } = options;

    let browser = null;
    let page = null;

    try {
      // Initialize browser
      browser = await this.initBrowser();
      page = await browser.newPage();

      // Generate HTML template
      const htmlTemplate = generateReportTemplate(templateSettings);
      const componentsHTML = generateComponentsHTML(components);

      // Combine template with components
      const fullHTML = htmlTemplate.replace(
        '<!-- Content will be injected here -->',
        componentsHTML
      );

      // Set content
      await page.setContent(fullHTML, {
        waitUntil: 'networkidle'
      });

      // Wait for any dynamic content to load
      await page.waitForTimeout(2000);

      // Check if 13-month trailing profit and loss report is included
      const has13MonthTrailing = Boolean(components.profitLoss13Month ||
                                         (componentsHTML && componentsHTML.includes('13 Month Trailing')) ||
                                         (componentsHTML && componentsHTML.includes('13-Month-Trailing')));

      // Generate PDF with proper settings
      const pdfBuffer = await page.pdf({
        format: 'A4', // Changed to A4 format as requested
        landscape: has13MonthTrailing, // Apply landscape orientation for 13-month trailing P&L
        printBackground: true,
        margin: {
          top: '10mm',    // Reduced from 20mm
          right: '10mm',  // Reduced from 15mm
          bottom: '10mm', // Reduced from 20mm
          left: '10mm'    // Reduced from 15mm
        },
        displayHeaderFooter: false, // Removed headers and footers
        preferCSSPageSize: false
      });

      return pdfBuffer;

    } catch (error) {
      console.error('Error generating PDF:', error);
      throw new Error(`PDF generation failed: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  /**
   * Generate PDF from URL (for frontend rendering)
   * @param {Object} options - PDF generation options
   * @param {string} options.url - URL to render as PDF
   * @param {string} options.companyName - Company name for the report
   * @param {string} options.reportTitle - Title of the report
   * @param {string} options.selector - CSS selector to wait for before generating PDF
   * @param {string} options.htmlContent - Base64 encoded HTML content for faster generation
   * @returns {Buffer} - PDF buffer
   */
  async generatePDFFromURL(options) {
    const {
      url,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      selector = '.report-container',
      htmlContent
    } = options;

    let browser = null;
    let page = null;

    try {
      // Initialize browser
      browser = await this.initBrowser();
      page = await browser.newPage();

      // Set viewport for consistent rendering
      await page.setViewportSize({ width: 1200, height: 800 });

      // If base64 HTML content is provided, use it directly for faster generation
      if (htmlContent) {
        console.log('Using provided HTML content for faster PDF generation');
        try {
          const decodedHTML = Buffer.from(htmlContent, 'base64').toString('utf-8');
          await page.setContent(decodedHTML, {
            waitUntil: 'networkidle',
            timeout: 30000
          });
        } catch (decodeError) {
          console.error('Error decoding HTML content, falling back to URL method:', decodeError);
          // Fall back to URL method if base64 decoding fails
          await this.navigateToURL(page, url, selector);
        }
      } else {
        // Use URL navigation method
        await this.navigateToURL(page, url, selector);
      }

      // Hide header and sidebar elements that shouldn't be in PDF
      await page.addStyleTag({
        content: `
          .sidebar, .header, .navigation, .template-settings {
            display: none !important;
          }
          .report-content {
            margin-left: 0 !important;
            width: 100% !important;
          }
          body {
            background-color: white !important;
          }
        `
      });

      // Check if the page contains 13-month trailing profit and loss content
      const pageContent = await page.content();
      const has13MonthTrailing = Boolean(pageContent.includes('13 Month Trailing') ||
                                         pageContent.includes('13-Month-Trailing') ||
                                         url.includes('monthTrailing') ||
                                         url.includes('13-month'));

      // Generate PDF
      const pdfBuffer = await page.pdf({
        format: 'A4', // Changed to A4 format as requested
        landscape: has13MonthTrailing, // Apply landscape orientation for 13-month trailing P&L
        printBackground: true,
        margin: {
          top: '10mm',    // Reduced from 20mm
          right: '10mm',  // Reduced from 15mm
          bottom: '10mm', // Reduced from 20mm
          left: '10mm'    // Reduced from 15mm
        },
        displayHeaderFooter: false, // Removed headers and footers
        preferCSSPageSize: false
      });

      return pdfBuffer;

    } catch (error) {
      console.error('Error generating PDF from URL:', error);
      throw new Error(`PDF generation from URL failed: ${error.message}`);
    } finally {
      if (page) {
        await page.close();
      }
    }
  }

  /**
   * Save PDF buffer to file
   * @param {Buffer} pdfBuffer - PDF buffer
   * @param {string} filename - Output filename
   * @param {string} outputDir - Output directory (optional)
   * @returns {string} - File path
   */
  async savePDFToFile(pdfBuffer, filename, outputDir = './temp') {
    try {
      // Ensure output directory exists
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }

      const filePath = path.join(outputDir, filename);
      fs.writeFileSync(filePath, pdfBuffer);
      
      return filePath;
    } catch (error) {
      console.error('Error saving PDF to file:', error);
      throw new Error(`Failed to save PDF: ${error.message}`);
    }
  }
}

// Create singleton instance
export const pdfGenerationService = new PDFGenerationService();

// Cleanup on process exit
process.on('exit', async () => {
  await pdfGenerationService.closeBrowser();
});

process.on('SIGINT', async () => {
  await pdfGenerationService.closeBrowser();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await pdfGenerationService.closeBrowser();
  process.exit(0);
});
