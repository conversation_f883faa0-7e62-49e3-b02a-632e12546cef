import axiosInstance from "./axiosInstance";

/**
 * Generate PDF from report components
 * @param {Object} options - PDF generation options
 * @param {Object} options.templateSettings - Template styling settings
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @param {string} options.companyId - Company ID for URL generation
 * @param {string} options.htmlContent - Base64 encoded HTML content for faster generation
 * @returns {Promise<Blob>} - PDF blob for download
 */
export const generateReportPDF = async (options) => {
  try {
    const {
      templateSettings,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      companyId,
      htmlContent
    } = options;

    const payload = {
      templateSettings,
      companyName,
      reportTitle,
      companyId
    };

    // Add base64 HTML content if provided for faster generation
    if (htmlContent) {
      payload.htmlContent = htmlContent;
    }

    const response = await axiosInstance.post('/pdf/generate', payload, {
      responseType: 'blob', // Important for file downloads
      timeout: 90000 // Increased timeout to 90 seconds for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(
      error.response?.data?.message ||
      'Failed to generate PDF. Please try again.'
    );
  }
};

/**
 * Generate PDF from URL
 * @param {Object} options - PDF generation options
 * @param {string} options.url - URL to render as PDF
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @param {string} options.selector - CSS selector to wait for
 * @param {string} options.htmlContent - Base64 encoded HTML content for faster generation
 * @returns {Promise<Blob>} - PDF blob for download
 */
export const generatePDFFromURL = async (options) => {
  try {
    const {
      url,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      selector = '.report-container',
      htmlContent
    } = options;

    const payload = {
      url,
      companyName,
      reportTitle,
      selector
    };

    // Add base64 HTML content if provided for faster generation
    if (htmlContent) {
      payload.htmlContent = htmlContent;
    }

    const response = await axiosInstance.post('/pdf/generate-from-url', payload, {
      responseType: 'blob', // Important for file downloads
      timeout: 90000 // Increased timeout to 90 seconds for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating PDF from URL:', error);
    throw new Error(
      error.response?.data?.message ||
      'Failed to generate PDF from URL. Please try again.'
    );
  }
};

/**
 * Generate PDF and store in S3
 * @param {Object} options - PDF generation options
 * @param {Object} options.templateSettings - Template styling settings
 * @param {string} options.companyName - Company name for the report
 * @param {string} options.reportTitle - Title of the report
 * @param {string} options.companyId - Company ID for URL generation
 * @param {string} options.htmlContent - Base64 encoded HTML content for faster generation
 * @returns {Promise<Object>} - S3 file information
 */
export const generateAndStorePDF = async (options) => {
  try {
    const {
      templateSettings,
      companyName = 'Company Report',
      reportTitle = 'Financial Report',
      companyId,
      htmlContent
    } = options;

    const payload = {
      templateSettings,
      companyName,
      reportTitle,
      companyId
    };

    // Add base64 HTML content if provided for faster generation
    if (htmlContent) {
      payload.htmlContent = htmlContent;
    }

    const response = await axiosInstance.post('/pdf/generate-and-store', payload, {
      timeout: 90000 // Increased timeout to 90 seconds for PDF generation
    });

    return response.data;
  } catch (error) {
    console.error('Error generating and storing PDF:', error);
    throw new Error(
      error.response?.data?.message ||
      'Failed to generate and store PDF. Please try again.'
    );
  }
};

/**
 * Download blob as file
 * @param {Blob} blob - File blob
 * @param {string} filename - Filename for download
 */
export const downloadBlob = (blob, filename) => {
  try {
    // Create blob URL
    const url = window.URL.createObjectURL(blob);
    
    // Create temporary link element
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    
    // Append to body, click, and remove
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up blob URL
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading file:', error);
    throw new Error('Failed to download file. Please try again.');
  }
};

/**
 * Generate filename for PDF
 * @param {string} companyName - Company name
 * @param {string} reportTitle - Report title
 * @returns {string} - Generated filename
 */
export const generatePDFFilename = (companyName, reportTitle) => {
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
  const sanitizedCompany = companyName.replace(/[^a-zA-Z0-9]/g, '-');
  const sanitizedTitle = reportTitle.replace(/[^a-zA-Z0-9]/g, '-');

  return `${sanitizedCompany}-${sanitizedTitle}-${timestamp}.pdf`;
};

/**
 * Generate base64 encoded HTML content from current page for faster PDF generation
 * @param {string} selector - CSS selector for the content to capture
 * @returns {string} - Base64 encoded HTML content
 */
export const generateBase64HTMLContent = (selector = '.report-container') => {
  try {
    const element = document.querySelector(selector);
    if (!element) {
      console.warn(`Element with selector "${selector}" not found`);
      return null;
    }

    // Get the complete HTML including styles
    const styles = Array.from(document.styleSheets)
      .map(styleSheet => {
        try {
          return Array.from(styleSheet.cssRules)
            .map(rule => rule.cssText)
            .join('\n');
        } catch (e) {
          // Handle cross-origin stylesheets
          return '';
        }
      })
      .join('\n');

    // Create complete HTML document
    const htmlContent = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Financial Report</title>
        <style>
          ${styles}
          body { margin: 0; padding: 20px; background: white; }
          .report-container { width: 100%; }
        </style>
      </head>
      <body>
        ${element.outerHTML}
      </body>
      </html>
    `;

    // Convert to base64 (using simpler approach)
    return btoa(htmlContent);
  } catch (error) {
    console.error('Error generating base64 HTML content:', error);
    return null;
  }
};
