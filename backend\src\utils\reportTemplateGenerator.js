/**
 * Utility for generating HTML templates for report PDF generation
 */

/**
 * Generate HTML template for report with proper styling and page breaks
 * @param {Object} templateSettings - The template settings for styling
 * @returns {string} - HTML template string
 */
export const generateReportTemplate = (templateSettings) => {
  const {
    header = {},
    heading = {},
    subHeading = {},
    content = {}
  } = templateSettings || {};

  // Default styles if not provided
  const defaultSettings = {
    header: {
      fontStyle: 'Calibri',
      fontType: 'Bold',
      fontSize: 44,
      color: '#1e7c8c'
    },
    heading: {
      fontStyle: 'Calibri',
      fontType: 'Bold',
      fontSize: 36,
      color: '#1e7c8c'
    },
    subHeading: {
      fontStyle: 'Arial',
      fontType: 'Bold',
      fontSize: 22,
      color: '#1e7c8c'
    },
    content: {
      fontStyle: 'Arial',
      fontType: 'Regular',
      fontSize: 16,
      color: '#333333'
    }
  };

  // Merge with defaults
  const mergedSettings = {
    header: { ...defaultSettings.header, ...header },
    heading: { ...defaultSettings.heading, ...heading },
    subHeading: { ...defaultSettings.subHeading, ...subHeading },
    content: { ...defaultSettings.content, ...content }
  };

  // Create CSS styles
  const styles = `
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 0;
      background-color: white;
    }
    .page-break {
      page-break-after: always;
    }
    .report-container {
      padding: 10px; /* Reduced from 20px */
      max-width: 100%;
      margin: 0 auto;
    }
    .header {
      font-family: ${mergedSettings.header.fontStyle}, sans-serif;
      font-weight: ${mergedSettings.header.fontType === 'Bold' ? 'bold' : 'normal'};
      font-size: ${mergedSettings.header.fontSize}px;
      color: ${mergedSettings.header.color};
      margin-bottom: 20px;
    }
    .heading {
      font-family: ${mergedSettings.heading.fontStyle}, sans-serif;
      font-weight: ${mergedSettings.heading.fontType === 'Bold' ? 'bold' : 'normal'};
      font-size: ${mergedSettings.heading.fontSize}px;
      color: ${mergedSettings.heading.color};
      margin-bottom: 15px;
    }
    .sub-heading {
      font-family: ${mergedSettings.subHeading.fontStyle}, sans-serif;
      font-weight: ${mergedSettings.subHeading.fontType === 'Bold' ? 'bold' : 'normal'};
      font-size: ${mergedSettings.subHeading.fontSize}px;
      color: ${mergedSettings.subHeading.color};
      margin-bottom: 10px;
    }
    .content {
      font-family: ${mergedSettings.content.fontStyle}, sans-serif;
      font-weight: ${mergedSettings.content.fontType === 'Bold' ? 'bold' : 'normal'};
      font-size: ${mergedSettings.content.fontSize}px;
      color: ${mergedSettings.content.color};
      line-height: 1.6;
      margin-bottom: 10px;
    }

    /* Table styling with repeating headers */
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 15px; /* Reduced from 20px */
    }

    /* Ensure table headers repeat on page breaks */
    thead {
      display: table-header-group;
    }

    tbody {
      display: table-row-group;
    }

    th, td {
      padding: 6px; /* Reduced from 8px */
      text-align: left;
      border-bottom: 1px solid #ddd;
      font-family: ${mergedSettings.content.fontStyle}, sans-serif;
      font-size: ${mergedSettings.content.fontSize - 2}px;
      color: ${mergedSettings.content.color};
    }

    th {
      background-color: #20b2aa !important; /* Use consistent header color */
      color: white !important;
      font-weight: bold;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    /* Specific styling for report component headers */
    .component-header {
      background-color: #20b2aa !important;
      color: white !important;
      font-weight: bold;
      padding: 8px;
      margin: 10px 0 5px 0; /* Reduced margins */
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
    }

    /* Ensure consistent styling for fiscal year, liquidity, operational efficiency headers */
    .fiscal-year-header,
    .liquidity-summary-header,
    .operational-efficiency-header,
    .profit-loss-header {
      background-color: #20b2aa !important;
      color: white !important;
      font-weight: bold;
      padding: 8px;
      margin: 10px 0 5px 0;
      -webkit-print-color-adjust: exact;
      print-color-adjust: exact;
      page-break-inside: avoid;
    }

    .chart-container {
      margin: 15px 0; /* Reduced from 20px */
      page-break-inside: avoid;
    }

    /* Page break handling */
    .avoid-break {
      page-break-inside: avoid;
    }

    .break-before {
      page-break-before: always;
    }

    /* Reduce overall spacing */
    .section {
      margin-bottom: 15px; /* Reduced spacing between sections */
    }
    @media print {
      /* Ensure component headers are visible and repeat */
      .component-header {
        position: relative;
        z-index: 100;
        page-break-inside: avoid;
        page-break-after: avoid;
        display: block !important;
        background-color: #20b2aa !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .component-header h2 {
        margin: 0;
        padding: 8px;
        color: white !important;
        font-size: 16px;
        font-weight: bold;
      }

      /* Ensure table headers repeat on each page */
      table {
        page-break-inside: auto;
      }

      thead {
        display: table-header-group;
        page-break-inside: avoid;
      }

      tbody {
        display: table-row-group;
      }

      tr {
        page-break-inside: avoid;
        page-break-after: auto;
      }

      th {
        page-break-inside: avoid;
        page-break-after: avoid;
        background-color: #20b2aa !important;
        color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      tfoot {
        display: table-footer-group;
      }

      /* Ensure sections start appropriately */
      .section {
        page-break-before: auto;
        page-break-inside: avoid;
      }
    }
  `;

  // Create HTML template
  const htmlTemplate = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Financial Report</title>
      <style>${styles}</style>
    </head>
    <body>
      <div class="report-container" id="report-content">
        <!-- Content will be injected here -->
      </div>
    </body>
    </html>
  `;

  return htmlTemplate;
};

/**
 * Generate HTML content for each report component
 * @param {Object} components - Object containing all report components
 * @returns {string} - HTML content string
 */
export const generateComponentsHTML = (components) => {
  const {
    tableOfContents,
    reportSummary,
    fiscalYear,
    expenseSummary,
    operationalEfficiency,
    liquiditySummary,
    profitLoss13Month,
    profitLossMonthly,
    profitLossYTD,
    balanceSheet
  } = components || {};

  let html = '';

  // Add each component with page breaks
  if (tableOfContents) {
    html += `
      <div id="table-of-contents">
        ${tableOfContents}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (reportSummary) {
    html += `
      <div id="report-summary">
        ${reportSummary}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (fiscalYear) {
    html += `
      <div id="fiscal-year" class="section">
        <div class="fiscal-year-header component-header">
          <h2>Current Fiscal Year</h2>
        </div>
        ${fiscalYear}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (expenseSummary) {
    html += `
      <div id="expense-summary" class="section">
        <div class="expense-summary-header component-header">
          <h2>Expense Summary</h2>
        </div>
        ${expenseSummary}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (operationalEfficiency) {
    html += `
      <div id="operational-efficiency" class="section">
        <div class="operational-efficiency-header component-header">
          <h2>Operational Efficiency</h2>
        </div>
        ${operationalEfficiency}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (liquiditySummary) {
    html += `
      <div id="liquidity-summary" class="section">
        <div class="liquidity-summary-header component-header">
          <h2>Liquidity Summary</h2>
        </div>
        ${liquiditySummary}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (profitLoss13Month) {
    html += `
      <div id="profit-loss-13-month" class="section">
        <div class="profit-loss-header component-header">
          <h2>Profit and Loss - 13 Month Trailing</h2>
        </div>
        ${profitLoss13Month}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (profitLossMonthly) {
    html += `
      <div id="profit-loss-monthly" class="section">
        <div class="profit-loss-header component-header">
          <h2>Profit and Loss - Monthly</h2>
        </div>
        ${profitLossMonthly}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (profitLossYTD) {
    html += `
      <div id="profit-loss-ytd" class="section">
        <div class="profit-loss-header component-header">
          <h2>Profit and Loss - Year to Date</h2>
        </div>
        ${profitLossYTD}
      </div>
      <div class="page-break"></div>
    `;
  }

  if (balanceSheet) {
    html += `
      <div id="balance-sheet" class="section">
        <div class="balance-sheet-header component-header">
          <h2>Balance Sheet</h2>
        </div>
        ${balanceSheet}
      </div>
    `;
  }

  return html;
};

/**
 * Generate a special URL for PDF generation that includes template settings
 * @param {string} baseUrl - Base URL of the frontend
 * @param {Object} templateSettings - Template settings to apply
 * @param {string} companyId - Company ID for the URL path
 * @returns {string} - URL with template settings as query parameters
 */
export const generatePDFUrl = (baseUrl, templateSettings, companyId = '1') => {
  const url = new URL(`/company/${companyId}/custom-template`, baseUrl);

  // Add template settings as query parameters
  if (templateSettings) {
    url.searchParams.set('templateSettings', JSON.stringify(templateSettings));
    url.searchParams.set('pdfMode', 'true'); // Flag to indicate PDF generation mode
  }

  return url.toString();
};
